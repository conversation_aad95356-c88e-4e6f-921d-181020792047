"""
红色靶心识别系统 - 集成版本
功能：红色靶心识别、靶环检测、矩形检测、三角形生成
作者：AI Assistant
版本：1.1
"""

from media.sensor import *
import time
from media.display import *
from media.media import *
import math
from machine import UART
from machine import FPIOA

# ========================= 配置参数 =========================
# 硬件配置
SENSOR_WIDTH, SENSOR_HEIGHT = 640, 480  # 传感器分辨率
FRAME_WIDTH, FRAME_HEIGHT = 320, 240     # 帧分辨率
UART_TX_PIN, UART_RX_PIN = 3, 4          # 串口引脚
UART_BAUDRATE = 115200                    # 串口波特率
LCD_ENABLE = True                         # 启用LCD显示

# 图像处理参数
BINARY_THRESHOLD = 120                    # 二值化阈值
RED_THRESHOLD = (100, 32, 11, 127, -128, 127)    # 红色LAB阈值(30, 66, 39, 58, -29, 44)

# 矩形检测参数
MIN_RECT_AREA, MAX_RECT_AREA = 20000, 30000     # 矩形面积范围
MIN_ASPECT_RATIO, MAX_ASPECT_RATIO = 0.4, 1.6   # 矩形宽高比范围

# 三角形生成参数
TRIANGLE_SCALE = 0.6                      # 三角形占矩形的比例
POINTS_PER_EDGE = 4                       # 每条边插值点数

# 红色靶心检测参数
TARGET_RADII = [2, 4, 6, 8, 10]          # 靶环半径(cm)
BULLSEYE_MAX_DIAMETER = 0.1               # 靶心最大直径(cm)（已弃用）
PIXEL_TO_CM = 0.05                        # 像素到厘米转换比例
RING_DETECTION_TOLERANCE = 0.2            # 靶环检测误差容忍度
RED_BLOB_PIXELS_THRESHOLD = 100           # 红色色块像素阈值（提高以过滤噪点）
RED_BLOB_AREA_THRESHOLD = 200             # 红色色块面积阈值（提高以过滤噪点）

# 显示颜色定义 (RGB565)
COLOR_WHITE = (255, 255, 255)
COLOR_RED = (255, 0, 0)
COLOR_GREEN = (0, 255, 0)
COLOR_YELLOW = (255, 255, 0)
COLOR_CYAN = (0, 255, 255)
COLOR_ORANGE = (255, 100, 0)

# 绘制参数
BULLSEYE_CIRCLE_RADIUS = 5                # 靶心圆圈半径
BULLSEYE_LINE_THICKNESS = 3               # 靶心线条粗细
RING_LINE_THICKNESS = 2                   # 靶环线条粗细
CONNECTION_LINE_THICKNESS = 1             # 连接线粗细
FONT_SIZE_LARGE, FONT_SIZE_MEDIUM, FONT_SIZE_SMALL = 20, 15, 12  # 字体大小

# 串口数据格式
PACKET_START, PACKET_END = "$$", "##"     # 数据包起止标识
TYPE_TRIANGLE, TYPE_BULLSEYE, TYPE_RING = "M", "B", "R"  # 数据类型

# 帧间隔设置
FRAME_INTERVAL = 2                       # 帧间隔（处理间隔帧数，1=每帧处理，2=每2帧处理一次）
ENABLE_FRAME_SKIP = True                 # 启用帧跳过功能

# 调试设置
SHOW_DEBUG_INFO = True                    # 显示调试信息
SHOW_FPS = True                          # 显示帧率
SHOW_DETECTION_COUNT = True              # 显示检测计数
ENABLE_UART_SEND = True                  # 启用串口发送

# 位置滤波设置
ENABLE_POSITION_FILTER = True            # 启用位置滤波
FILTER_WINDOW_SIZE = 5                   # 移动平均窗口大小
MIN_CONFIDENCE_THRESHOLD = 80            # 最低置信度阈值（降低阈值）
MAX_POSITION_JUMP = 50                   # 最大位置跳跃阈值(像素)
POSITION_STABILITY_WEIGHT = 0.3          # 位置稳定性权重

# 形态学处理设置
ENABLE_MORPHOLOGY = True                 # 启用形态学处理
DILATE_KERNEL_SIZE = 2                   # 膨胀核大小
CLOSE_ITERATIONS = 1                     # 闭运算迭代次数

# 圆检测设置
ENABLE_CIRCLE_DETECTION = True           # 启用霍夫圆检测
CIRCLE_THRESHOLD = 5000                  # 圆检测阈值
MIN_CIRCLE_RADIUS = int(1.5 / PIXEL_TO_CM)  # 最小圆半径(1.5cm)
MAX_CIRCLE_RADIUS = int(12 / PIXEL_TO_CM)    # 最大圆半径(12cm)
MAX_CIRCLES_DETECT = 10                  # 最大检测圆数量

# --------------------------- 硬件初始化 ---------------------------
fpioa = FPIOA()
fpioa.set_function(UART_TX_PIN, FPIOA.UART1_TXD)
fpioa.set_function(UART_RX_PIN, FPIOA.UART1_RXD)
uart = UART(UART.UART1, UART_BAUDRATE)

sensor = Sensor(width=SENSOR_WIDTH, height=SENSOR_HEIGHT)
sensor.reset()
sensor.set_framesize(width=FRAME_WIDTH, height=FRAME_HEIGHT)
sensor.set_pixformat(Sensor.RGB565)

if LCD_ENABLE:
    Display.init(Display.ST7701, to_ide=True)
else:
    Display.init(Display.VIRT, sensor.width(), sensor.height())

MediaManager.init()
sensor.run()

# --------------------------- 位置滤波类 ---------------------------
class PositionFilter:
    """位置滤波器，用于平滑靶心位置"""
    def __init__(self, window_size=5):
        self.window_size = window_size
        self.position_history = []  # 位置历史记录
        self.confidence_history = []  # 置信度历史记录
        self.last_valid_position = None  # 上一个有效位置
        self.last_valid_confidence = 0

    def update(self, position, confidence):
        """更新位置和置信度"""
        if position is None:
            return self.last_valid_position

        # 检查位置跳跃
        if (self.last_valid_position and
            distance(position, self.last_valid_position) > MAX_POSITION_JUMP):
            print("位置跳跃过大，使用滤波位置")
            return self.get_filtered_position()

        # 添加到历史记录
        self.position_history.append(position)
        self.confidence_history.append(confidence)

        # 保持窗口大小
        if len(self.position_history) > self.window_size:
            self.position_history.pop(0)
            self.confidence_history.pop(0)

        # 计算加权平均位置
        filtered_pos = self.get_filtered_position()

        # 更新有效位置
        if confidence >= MIN_CONFIDENCE_THRESHOLD:
            self.last_valid_position = filtered_pos
            self.last_valid_confidence = confidence

        return filtered_pos

    def get_filtered_position(self):
        """获取滤波后的位置"""
        if not self.position_history:
            return self.last_valid_position

        if len(self.position_history) == 1:
            return self.position_history[0]

        # 加权移动平均
        total_weight = 0
        weighted_x = 0
        weighted_y = 0

        for i, (pos, conf) in enumerate(zip(self.position_history, self.confidence_history)):
            # 越新的位置权重越大
            time_weight = (i + 1) / len(self.position_history)
            # 置信度越高权重越大
            conf_weight = conf / 100.0
            # 综合权重
            weight = time_weight * conf_weight

            weighted_x += pos[0] * weight
            weighted_y += pos[1] * weight
            total_weight += weight

        if total_weight > 0:
            return (int(weighted_x / total_weight), int(weighted_y / total_weight))
        else:
            return self.position_history[-1]

# --------------------------- 辅助函数 ---------------------------
def get_ring_radius_px(radius_cm):
    """获取靶环的像素半径"""
    return int(radius_cm / PIXEL_TO_CM)

def get_detection_tolerance_px(radius_px):
    """获取检测误差容忍度(像素)"""
    return radius_px * RING_DETECTION_TOLERANCE

def calculate_center(points):
    """计算点集的中心"""
    if not points:
        return (0, 0)
    sum_x = sum(p[0] for p in points)
    sum_y = sum(p[1] for p in points)
    return (sum_x / len(points), sum_y / len(points))

def distance(p1, p2):
    """计算两点间距离"""
    dx, dy = p2[0] - p1[0], p2[1] - p1[1]
    return math.sqrt(dx*dx + dy*dy)

# --------------------------- 检测函数 ---------------------------
def detect_rectangles(binary_img):
    """检测矩形"""
    rectangles = []
    for r in binary_img.find_rects(threshold=10000):
        rect_width, rect_height = r.w(), r.h()
        rect_area = rect_width * rect_height

        if MIN_RECT_AREA <= rect_area <= MAX_RECT_AREA:
            if MIN_ASPECT_RATIO < (rect_width / rect_height) < MAX_ASPECT_RATIO:
                rectangles.append({
                    "corners": r.corners(),
                    "width": rect_width,
                    "height": rect_height,
                    "area": rect_area
                })
    return rectangles



def detect_red_blobs(img):
    """检测红色色块"""
    return img.find_blobs([RED_THRESHOLD], pixels_threshold=RED_BLOB_PIXELS_THRESHOLD,
                         area_threshold=RED_BLOB_AREA_THRESHOLD, merge=True)

def preprocess_red_image(img):
    """对红色图像进行形态学预处理"""
    # 转换为LAB色彩空间并提取红色
    red_binary = img.binary([RED_THRESHOLD])

    if ENABLE_MORPHOLOGY:
        print("应用形态学处理...")

        # 膨胀操作：连接断开的圆环
        if DILATE_KERNEL_SIZE > 0:
            red_binary.dilate(DILATE_KERNEL_SIZE)
            print("膨胀处理完成，核大小:", DILATE_KERNEL_SIZE)

        # 闭运算：填充圆环内部的小间隙
        if CLOSE_ITERATIONS > 0:
            for i in range(CLOSE_ITERATIONS):
                red_binary.close(1)  # 使用1像素核进行闭运算
            print("闭运算完成，迭代次数:", CLOSE_ITERATIONS)

    return red_binary

def detect_circles_hough(img):
    """使用霍夫变换检测圆形"""
    if not ENABLE_CIRCLE_DETECTION:
        return []

    try:
        # 对图像进行预处理
        processed_img = preprocess_red_image(img)

        # 使用霍夫圆检测
        circles = processed_img.find_circles(
            threshold=CIRCLE_THRESHOLD,
            x_margin=10, y_margin=10,
            r_margin=10,
            r_min=MIN_CIRCLE_RADIUS,
            r_max=MAX_CIRCLE_RADIUS,
            r_step=2
        )

        print("霍夫圆检测结果: 找到", len(circles), "个圆")

        # 输出检测到的圆的详细信息
        for i, circle in enumerate(circles):
            print("  圆%d: 中心(%d,%d), 半径=%d, 强度=%d" %
                  (i, circle.x(), circle.y(), circle.r(), circle.magnitude()))

        return circles[:MAX_CIRCLES_DETECT]  # 限制最大检测数量

    except Exception as e:
        print("霍夫圆检测失败:", str(e))
        return []

def calculate_bullseye_from_circles(circles):
    """从检测到的圆计算靶心位置"""
    if not circles:
        return None, 0

    # 过滤合理的圆（基于半径范围）
    valid_circles = []
    for circle in circles:
        radius_cm = circle.r() * PIXEL_TO_CM
        if 1.0 < radius_cm < 15.0:  # 合理的半径范围
            valid_circles.append(circle)

    if not valid_circles:
        print("没有找到合理半径的圆")
        return None, 0

    print("有效圆数量:", len(valid_circles))

    # 计算所有圆心的加权平均位置
    total_weight = 0
    weighted_x = 0
    weighted_y = 0

    for circle in valid_circles:
        # 权重基于圆的强度和半径合理性
        magnitude_weight = min(circle.magnitude() / 1000.0, 1.0)  # 强度权重
        radius_cm = circle.r() * PIXEL_TO_CM

        # 半径权重：2-10cm范围内的圆权重更高
        if 2.0 <= radius_cm <= 10.0:
            radius_weight = 1.0
        elif 1.5 <= radius_cm <= 12.0:
            radius_weight = 0.7
        else:
            radius_weight = 0.3

        weight = magnitude_weight * radius_weight

        weighted_x += circle.x() * weight
        weighted_y += circle.y() * weight
        total_weight += weight

        print("  圆心(%d,%d), 半径=%.1fcm, 权重=%.2f" %
              (circle.x(), circle.y(), radius_cm, weight))

    if total_weight > 0:
        bullseye_x = int(weighted_x / total_weight)
        bullseye_y = int(weighted_y / total_weight)

        # 计算置信度
        confidence = min(100, len(valid_circles) * 20 + total_weight * 30)

        print("计算得到靶心位置: (%d, %d), 置信度: %.1f" %
              (bullseye_x, bullseye_y, confidence))

        return (bullseye_x, bullseye_y), confidence

    return None, 0



def detect_bullseye_and_rings(img):
    """优化的同心圆靶心检测算法（集成圆检测和色块检测）"""
    print("=== 开始靶心检测 ===")

    # 方法1：霍夫圆检测（主要方法）
    circles = detect_circles_hough(img)
    circle_bullseye, circle_confidence = calculate_bullseye_from_circles(circles)

    # 方法2：传统色块检测（备选方法）
    red_blobs = detect_red_blobs(img)
    blob_bullseye, blob_confidence = detect_bullseye_from_blobs(red_blobs)

    # 选择最佳检测结果
    if circle_confidence >= 60 and circle_bullseye:
        print("使用圆检测结果，置信度:", circle_confidence)
        # 创建虚拟色块对象用于显示
        class CircleBullseye:
            def __init__(self, pos):
                self.pos = pos
            def cx(self): return self.pos[0]
            def cy(self): return self.pos[1]
            def x(self): return self.pos[0] - 10
            def y(self): return self.pos[1] - 10
            def __getitem__(self, key):
                # 返回一个虚拟的矩形区域
                return [self.pos[0]-20, self.pos[1]-20, 40, 40][key]

        return CircleBullseye(circle_bullseye), [], circle_confidence

    elif blob_confidence >= MIN_CONFIDENCE_THRESHOLD and blob_bullseye:
        print("使用色块检测结果，置信度:", blob_confidence)
        return blob_bullseye, [], blob_confidence

    else:
        print("两种方法都未找到符合条件的靶心")
        print("圆检测置信度:", circle_confidence, "色块检测置信度:", blob_confidence)
        return None, [], 0

def detect_bullseye_from_blobs(red_blobs):
    """从色块检测靶心（原有方法）"""
    if not red_blobs:
        return None, 0

    # 调试输出：显示所有检测到的红色色块信息
    print("色块检测: 找到", len(red_blobs), "个红色色块")
    for i, blob in enumerate(red_blobs):
        print("  色块%d: w=%d, h=%d, pixels=%d, area=%d, density=%.3f" %
              (i, blob.w(), blob.h(), blob.pixels(), blob.area(), blob.density()))

    # 寻找最佳的同心圆靶心候选
    best_bullseye = None
    max_score = 0
    best_confidence = 0

    for blob in red_blobs:
        blob_pixels = blob.pixels()
        blob_w, blob_h = blob.w(), blob.h()
        aspect_ratio = blob_w / blob_h if blob_h > 0 else 1
        blob_density = blob.density()
        blob_area = blob.area()

        # 同心圆靶心特征评分（优化权重）
        score = 0
        confidence_factors = []

        # 1. 面积评分：适中的面积更可能是完整靶心（权重35%）
        if 8000 < blob_area < 40000:  # 优化面积范围
            area_score = 35
            confidence_factors.append(0.9)
        elif 5000 < blob_area < 60000:  # 扩展范围
            area_score = 25
            confidence_factors.append(0.7)
        elif 2000 < blob_area < 80000:  # 最大范围
            area_score = 15
            confidence_factors.append(0.5)
        else:
            area_score = 0
            confidence_factors.append(0.2)
        score += area_score

        # 2. 形状评分：接近正方形的更可能是靶心（权重30%）
        if 0.8 < aspect_ratio < 1.25:  # 更严格的正方形要求
            shape_score = 30
            confidence_factors.append(0.9)
        elif 0.7 < aspect_ratio < 1.4:  # 接近正方形
            shape_score = 20
            confidence_factors.append(0.7)
        elif 0.5 < aspect_ratio < 2.0:  # 可接受的矩形
            shape_score = 10
            confidence_factors.append(0.5)
        else:
            shape_score = 0
            confidence_factors.append(0.2)
        score += shape_score

        # 3. 密度评分：同心圆靶心密度适中（权重25%）
        if 0.4 < blob_density < 0.7:  # 优化密度范围
            density_score = 25
            confidence_factors.append(0.9)
        elif 0.3 < blob_density < 0.8:  # 有空隙但不太稀疏
            density_score = 18
            confidence_factors.append(0.7)
        elif 0.2 < blob_density < 0.9:  # 扩展范围
            density_score = 10
            confidence_factors.append(0.5)
        else:
            density_score = 0
            confidence_factors.append(0.2)
        score += density_score

        # 4. 尺寸评分：合理的尺寸范围（权重10%）
        min_size = min(blob_w, blob_h)
        max_size = max(blob_w, blob_h)
        if 60 < min_size < 180 and 60 < max_size < 220:  # 优化尺寸范围
            size_score = 10
            confidence_factors.append(0.8)
        elif 50 < min_size < 200 and 50 < max_size < 250:
            size_score = 5
            confidence_factors.append(0.6)
        else:
            size_score = 0
            confidence_factors.append(0.3)
        score += size_score

        # 计算置信度（0-100）
        confidence = min(100, score + sum(confidence_factors) * 5)

        print("  -> 靶心评分: %d, 置信度: %.1f (面积:%d, 宽高比:%.2f, 密度:%.3f)" %
              (score, confidence, blob_area, aspect_ratio, blob_density))

        # 选择得分最高且置信度足够的作为靶心
        if score > max_score and confidence >= MIN_CONFIDENCE_THRESHOLD:
            max_score = score
            best_confidence = confidence
            best_bullseye = blob

    if not best_bullseye:
        print("未找到符合条件的同心圆靶心")
        return None, [], 0

    print("检测到同心圆靶心: 中心(%d, %d), 得分:%d, 置信度:%.1f" %
          (best_bullseye.cx(), best_bullseye.cy(), max_score, best_confidence))

    # 返回检测到的靶心和置信度
    return best_bullseye, [], best_confidence

# --------------------------- 透视变换函数 ---------------------------
def get_perspective_matrix(src_pts, dst_pts):
    """计算透视变换矩阵"""
    A, B = [], []
    for i in range(4):
        x, y = src_pts[i]
        u, v = dst_pts[i]
        A.append([x, y, 1, 0, 0, 0, -u*x, -u*y])
        A.append([0, 0, 0, x, y, 1, -v*x, -v*y])
        B.append(u)
        B.append(v)

    n = 8
    for i in range(n):
        max_row = i
        for j in range(i, len(A)):
            if abs(A[j][i]) > abs(A[max_row][i]):
                max_row = j
        A[i], A[max_row] = A[max_row], A[i]
        B[i], B[max_row] = B[max_row], B[i]

        pivot = A[i][i]
        if abs(pivot) < 1e-8:
            return None
        for j in range(i, n):
            A[i][j] /= pivot
        B[i] /= pivot

        for j in range(len(A)):
            if j != i and A[j][i] != 0:
                factor = A[j][i]
                for k in range(i, n):
                    A[j][k] -= factor * A[i][k]
                B[j] -= factor * B[i]

    return [[B[0], B[1], B[2]], [B[3], B[4], B[5]], [B[6], B[7], 1.0]]

def transform_points(points, matrix):
    """应用透视变换"""
    transformed = []
    for (x, y) in points:
        x_hom = x * matrix[0][0] + y * matrix[0][1] + matrix[0][2]
        y_hom = x * matrix[1][0] + y * matrix[1][1] + matrix[1][2]
        w_hom = x * matrix[2][0] + y * matrix[2][1] + matrix[2][2]
        if abs(w_hom) > 1e-8:
            transformed.append((x_hom / w_hom, y_hom / w_hom))
    return transformed

def generate_triangle(rect_corners):
    """生成三角形点阵"""
    rect_center = calculate_center(rect_corners)
    width = distance(rect_corners[1], rect_corners[0])
    height = distance(rect_corners[3], rect_corners[0])

    src_pts = [(0, height), (width, height), (width, 0), (0, 0)]
    virtual_center = (width/2, height/2)

    tri_size = min(width, height) * TRIANGLE_SCALE
    tri_height = tri_size * 0.866
    tri_vertices = [
        (virtual_center[0], virtual_center[1] - tri_height/2),
        (virtual_center[0] - tri_size/2, virtual_center[1] + tri_height/2),
        (virtual_center[0] + tri_size/2, virtual_center[1] + tri_height/2)
    ]

    # 生成三角形边上的点
    unique_points = set()
    all_points = []
    vertex_indices = []

    # 生成三条边的点
    for edge in range(3):
        p1 = tri_vertices[edge]
        p2 = tri_vertices[(edge + 1) % 3]
        start_j = 0 if edge == 0 else 1
        end_j = POINTS_PER_EDGE + 1 if edge == 0 else (POINTS_PER_EDGE + 1 if edge == 1 else POINTS_PER_EDGE)

        for j in range(start_j, end_j):
            t = j / POINTS_PER_EDGE
            x = p1[0] + t * (p2[0] - p1[0])
            y = p1[1] + t * (p2[1] - p1[1])

            x_rounded, y_rounded = round(x, 2), round(y, 2)
            if (x_rounded, y_rounded) not in unique_points:
                unique_points.add((x_rounded, y_rounded))
                all_points.append((x, y))
                if j == 0 or j == POINTS_PER_EDGE:
                    vertex_indices.append(len(all_points) - 1)

    matrix = get_perspective_matrix(src_pts, rect_corners)
    if not matrix:
        return [], rect_center, []

    transformed_points = transform_points(all_points, matrix)
    int_points = [(int(round(x)), int(round(y))) for x, y in transformed_points]

    return int_points, rect_center, vertex_indices

# --------------------------- 串口通信函数 ---------------------------
def send_triangle_points(points):
    """发送三角形点数据"""
    if not points or not ENABLE_UART_SEND:
        return
    count = len(points)
    msg = PACKET_START + TYPE_TRIANGLE + "," + str(count) + ","
    for x, y in points:
        msg += str(x) + "," + str(y) + ","
    msg = msg.rstrip(',') + PACKET_END
    uart.write(msg)
    print("发送三角形点:", msg)



def send_bullseye_data(bullseye):
    """发送靶心数据"""
    if not ENABLE_UART_SEND:
        return

    if bullseye:
        x, y = bullseye.cx(), bullseye.cy()
        msg = PACKET_START + TYPE_BULLSEYE + "," + str(x) + "," + str(y) + PACKET_END
        uart.write(msg)
        print("发送靶心:", msg)

# --------------------------- 主循环 ---------------------------
def main():
    """主程序循环"""
    clock = time.clock()
    print("同心圆靶心识别系统启动...")
    print("检测模式: 霍夫圆检测 + 色块检测")
    print("像素转换比例:", PIXEL_TO_CM, "cm/px")
    print("位置滤波:", "启用" if ENABLE_POSITION_FILTER else "禁用")
    print("形态学处理:", "启用" if ENABLE_MORPHOLOGY else "禁用")
    print("圆检测:", "启用" if ENABLE_CIRCLE_DETECTION else "禁用")
    print("圆检测半径范围: %.1f-%.1fcm" % (MIN_CIRCLE_RADIUS*PIXEL_TO_CM, MAX_CIRCLE_RADIUS*PIXEL_TO_CM))
    print("帧间隔设置:", FRAME_INTERVAL, "帧" if ENABLE_FRAME_SKIP else "(禁用)")

    # 初始化位置滤波器
    position_filter = PositionFilter(FILTER_WINDOW_SIZE) if ENABLE_POSITION_FILTER else None

    # 帧计数器
    frame_count = 0
    last_bullseye = None  # 保存上一次检测到的靶心
    last_confidence = 0   # 保存上一次的置信度

    while True:
        clock.tick()
        img = sensor.snapshot()
        frame_count += 1

        # 帧间隔控制：只在指定间隔帧进行检测
        should_process = not ENABLE_FRAME_SKIP or (frame_count % FRAME_INTERVAL == 0)

        if should_process:
            # 进行完整的检测处理
            gray = img.to_grayscale()
            binary = gray.binary([(0, BINARY_THRESHOLD)])

            # 检测红色同心圆靶心
            raw_bullseye, _, confidence = detect_bullseye_and_rings(img)

            # 应用位置滤波
            if position_filter and raw_bullseye:
                raw_position = (raw_bullseye.cx(), raw_bullseye.cy())
                filtered_position = position_filter.update(raw_position, confidence)

                # 创建滤波后的靶心对象（用于显示）
                class FilteredBullseye:
                    def __init__(self, pos, original_blob):
                        self.pos = pos
                        self.original = original_blob
                    def cx(self): return self.pos[0]
                    def cy(self): return self.pos[1]
                    def x(self): return self.pos[0] - 5
                    def y(self): return self.pos[1] - 5
                    def __getitem__(self, key): return self.original[key]

                bullseye = FilteredBullseye(filtered_position, raw_bullseye) if filtered_position else None
            else:
                bullseye = raw_bullseye

            # 更新缓存的检测结果
            if bullseye:
                last_bullseye = bullseye
                last_confidence = confidence
        else:
            # 跳过检测，使用上一次的结果
            bullseye = last_bullseye
            confidence = last_confidence

        if bullseye:
            # 绘制靶心标记
            img.draw_circle(bullseye.cx(), bullseye.cy(), BULLSEYE_CIRCLE_RADIUS, COLOR_RED, BULLSEYE_LINE_THICKNESS)
            img.draw_cross(bullseye.cx(), bullseye.cy(), COLOR_RED, BULLSEYE_LINE_THICKNESS)

            # 显示原始位置和滤波位置（如果启用滤波）
            if position_filter and raw_bullseye and (raw_bullseye.cx(), raw_bullseye.cy()) != (bullseye.cx(), bullseye.cy()):
                # 绘制原始检测位置（灰色）
                img.draw_circle(raw_bullseye.cx(), raw_bullseye.cy(), 3, COLOR_WHITE, 1)
                img.draw_string_advanced(bullseye.x(), bullseye.y()-40, FONT_SIZE_SMALL,
                                       "原始(" + str(raw_bullseye.cx()) + "," + str(raw_bullseye.cy()) + ")", COLOR_WHITE)
                # 滤波后位置标注
                img.draw_string_advanced(bullseye.x(), bullseye.y()-25, FONT_SIZE_MEDIUM,
                                       "靶心(" + str(bullseye.cx()) + "," + str(bullseye.cy()) + ") 置信度:" + str(int(confidence)), COLOR_RED)
            else:
                img.draw_string_advanced(bullseye.x(), bullseye.y()-25, FONT_SIZE_MEDIUM,
                                       "靶心(" + str(bullseye.cx()) + "," + str(bullseye.cy()) + ") 置信度:" + str(int(confidence)), COLOR_RED)

            # 可选：绘制靶心区域的外接矩形
            if hasattr(bullseye, 'original'):
                img.draw_rectangle(bullseye.original[0:4], COLOR_WHITE, 1)
            else:
                img.draw_rectangle(bullseye[0:4], COLOR_WHITE, 1)

            # 发送靶心数据
            send_bullseye_data(bullseye)

        # 矩形和三角形检测（也应用帧间隔控制）
        if should_process:
            # 检测矩形和三角形
            gray = img.to_grayscale()
            binary = gray.binary([(0, BINARY_THRESHOLD)])
            rectangles = detect_rectangles(binary)
        else:
            rectangles = []  # 跳过矩形检测
        for rect in rectangles:
            corners = rect["corners"]
            rect_area = rect["area"]
            rect_width = rect["width"]
            rect_height = rect["height"]

            ordered_corners = [corners[3], corners[2], corners[1], corners[0]]  # 左下,右下,右上,左上

            # 绘制矩形
            for i in range(4):
                p1, p2 = ordered_corners[i], ordered_corners[(i+1)%4]
                img.draw_line(p1[0], p1[1], p2[0], p2[1], COLOR_GREEN, 2)

            for i, (x, y) in enumerate(ordered_corners, 1):
                img.draw_circle(x, y, 5, COLOR_RED, 2)
                img.draw_string_advanced(x+5, y-10, FONT_SIZE_MEDIUM, str(i), COLOR_YELLOW)

            img.draw_string_advanced(ordered_corners[0][0], ordered_corners[0][1]+20, FONT_SIZE_MEDIUM,
                                   "Area: " + str(rect_area) + "px² (" + str(rect_width) + "x" + str(rect_height) + ")", COLOR_YELLOW)

            # 生成并绘制三角形点
            tri_points, rect_center, vertex_indices = generate_triangle(ordered_corners)
            if tri_points:
                # 绘制所有点
                for i, (x, y) in enumerate(tri_points):
                    is_vertex = i in vertex_indices
                    radius = 4 if is_vertex else 2
                    img.draw_circle(x, y, radius, COLOR_CYAN, 1)

                # 标记中心
                rx, ry = map(int, map(round, rect_center))
                img.draw_circle(rx, ry, 5, (0,0,255), 1)
                tri_center = calculate_center(tri_points)
                tx, ty = map(int, map(round, tri_center))
                img.draw_circle(tx, ty, 3, COLOR_RED, 1)

                # 发送三角形点到串口
                send_triangle_points(tri_points)

        # 显示检测信息
        if SHOW_DEBUG_INFO:
            info_y = 0
            if SHOW_FPS:
                img.draw_string_advanced(0, info_y, FONT_SIZE_LARGE, 'FPS: %.1f' % clock.fps(), COLOR_WHITE)
                info_y += 25

            # 显示帧间隔信息
            if ENABLE_FRAME_SKIP:
                frame_status = "处理" if should_process else "跳过"
                img.draw_string_advanced(0, info_y, FONT_SIZE_MEDIUM,
                                       'Frame: %d/%d (%s)' % (frame_count % FRAME_INTERVAL if FRAME_INTERVAL > 1 else 1, FRAME_INTERVAL, frame_status),
                                       COLOR_CYAN)
                info_y += 20

            if bullseye and SHOW_DETECTION_COUNT:
                img.draw_string_advanced(0, info_y, FONT_SIZE_MEDIUM, 'Bullseye: (' + str(bullseye.cx()) + ',' + str(bullseye.cy()) + ')', COLOR_RED)
                info_y += 20
                img.draw_string_advanced(0, info_y, FONT_SIZE_MEDIUM, 'Target detected: Concentric circles', COLOR_ORANGE)
            elif not bullseye:
                img.draw_string_advanced(0, info_y, FONT_SIZE_MEDIUM, 'No bullseye detected', COLOR_WHITE)

        Display.show_image(img)

# 启动主程序
if __name__ == "__main__":
    main()
