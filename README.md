# 红色靶心识别系统

## 功能概述
基于K210摄像头模块的视觉识别系统，专注于红色靶心和靶环检测。

## 主要功能

### 🎯 同心圆靶心识别（增强版）
- **双重检测算法**：霍夫圆检测 + 传统色块检测，互为备选
- **形态学预处理**：膨胀和闭运算连接断开的细圆环
- **霍夫圆检测**：直接检测圆形结构，解决圆环分割问题
- **智能评分系统**：基于面积、形状、密度等特征评分选择最佳靶心
- **位置滤波**：移动平均滤波减少位置抖动，提高稳定性
- **置信度评估**：实时评估检测质量，过滤低质量结果
- **异常值处理**：检测位置跳跃，自动使用滤波位置
- **精确中心定位**：使用几何中心作为靶心坐标
- **实时标注**：显示靶心坐标和置信度信息
- **数据传输**：通过串口发送稳定的靶心坐标

### 📐 矩形和三角形检测
- **矩形识别**：检测指定面积和宽高比的矩形
- **三角形生成**：在矩形内生成三角形点阵

## 新检测算法说明

### 算法原理（增强版）
1. **形态学预处理**：对红色二值图像进行膨胀和闭运算
2. **霍夫圆检测**：在预处理图像上检测圆形结构（主要方法）
3. **传统色块检测**：检测红色色块作为备选方案
4. **智能选择**：根据置信度选择最佳检测结果
5. **位置滤波**：使用加权移动平均平滑位置变化
6. **异常检测**：识别位置跳跃并使用滤波结果
7. **稳定输出**：输出经过滤波的稳定靶心坐标

### 优化评分标准
- **面积评分**：8000-40000像素²的优化面积范围（35分）
- **形状评分**：宽高比0.8-1.25的严格正方形要求（30分）
- **密度评分**：密度0.4-0.7的优化密度范围（25分）
- **尺寸评分**：60-180像素的精确尺寸范围（10分）
- **置信度阈值**：≥60分才被认定为有效靶心

### 位置滤波机制
- **滤波窗口**：保持最近5帧的位置和置信度历史
- **加权平均**：新帧权重更大，高置信度权重更大
- **跳跃检测**：超过50像素的位置变化被视为异常
- **稳定性保证**：低质量检测时保持上一帧的有效位置

## 关键参数

### 红色检测参数
```python
RED_THRESHOLD = (30, 100, 15, 127, 15, 127)  # 红色LAB阈值
TARGET_RADII = [2, 4, 6, 8, 10]              # 靶环半径(cm)
BULLSEYE_MAX_DIAMETER = 0.1                  # 靶心最大直径(cm)
PIXEL_TO_CM = 0.05                           # 像素转换比例
```

### 显示控制
```python
SHOW_DEBUG_INFO = True      # 显示调试信息
SHOW_FPS = True            # 显示帧率
ENABLE_UART_SEND = True    # 启用串口发送
```

### 位置滤波参数
```python
ENABLE_POSITION_FILTER = True      # 启用位置滤波
FILTER_WINDOW_SIZE = 5             # 移动平均窗口大小
MIN_CONFIDENCE_THRESHOLD = 50      # 最低置信度阈值
MAX_POSITION_JUMP = 50             # 最大位置跳跃阈值(像素)
POSITION_STABILITY_WEIGHT = 0.3    # 位置稳定性权重
```

### 形态学处理参数
```python
ENABLE_MORPHOLOGY = True           # 启用形态学处理
DILATE_KERNEL_SIZE = 2             # 膨胀核大小
CLOSE_ITERATIONS = 1               # 闭运算迭代次数
```

### 圆检测参数
```python
ENABLE_CIRCLE_DETECTION = True     # 启用霍夫圆检测
CIRCLE_THRESHOLD = 2000            # 圆检测阈值
MIN_CIRCLE_RADIUS = 30             # 最小圆半径(像素)
MAX_CIRCLE_RADIUS = 240            # 最大圆半径(像素)
MAX_CIRCLES_DETECT = 10            # 最大检测圆数量
```

## 使用方法

### 直接运行
```bash
# 在K210设备上运行
python vision_integrated.py
```

### 参数调整
编辑文件顶部的配置参数：

1. **校准像素转换比例**：
   - 放置已知直径的红色圆形物体
   - 调整 `PIXEL_TO_CM = 实际直径(cm) / 像素直径(px)`

2. **优化颜色检测**：
   - 根据环境光线调整 `RED_THRESHOLD`
   - 调整 `RED_BLOB_PIXELS_THRESHOLD` 和 `RED_BLOB_AREA_THRESHOLD`

## 显示标注

### 同心圆靶心显示（优化版）
- **靶心标记**：红色圆圈 + 十字标记 + "靶心(x,y) 置信度:XX"标签
- **原始位置**：白色小圆圈显示原始检测位置（启用滤波时）
- **靶心区域**：白色外接矩形框显示检测区域
- **滤波状态**：显示原始位置和滤波后位置的对比
- **调试信息**：显示"Target detected: Concentric circles"

### 矩形和三角形
- **矩形**：绿色边框 + 红色角点 + 面积信息
- **三角形**：青色点阵（顶点较大，边点较小）

## 标注信息说明

### 靶心标注
- **格式**：`靶心(x,y)`
- **颜色**：红色文字
- **位置**：靶心上方
- **示例**：`靶心(160,120)`

### 靶环标注
- **格式**：`靶环-半径Xcm(x,y)`
- **颜色**：橙色文字
- **位置**：靶环上方
- **示例**：`靶环-半径4cm(200,150)`

## 串口数据格式

```
$$B,x,y##           # 靶心：x,y坐标
$$M,count,x1,y1,x2,y2,...##  # 三角形：点数,坐标列表
```

## 调试功能

### 调试输出
程序运行时会输出详细的检测信息：
```
检测到 3 个红色色块:
  色块0: w=85, h=82, pixels=1250, area=6970, density=0.179
  -> 识别为潜在圆环: pixels=1250, density=0.179
  -> 匹配成功: 4cm环, 估算半径=42.5px, 误差=2.5px
```

### 参数调优指南
1. **像素数范围**：调整 `200 < blob_pixels < 20000`
2. **密度阈值**：调整 `blob_density < 0.8`
3. **宽高比范围**：调整 `0.3 < aspect_ratio < 3.0`
4. **容忍度系数**：调整 `best_match['radius_px'] * 0.3`

## 故障排除

1. **无法检测到圆环**
   - 观察调试输出中的色块信息
   - 检查 `RED_THRESHOLD` 颜色阈值设置
   - 调整像素数和密度过滤条件
   - 提高 `RED_BLOB_PIXELS_THRESHOLD` 和 `RED_BLOB_AREA_THRESHOLD`

2. **圆环匹配失败**
   - 检查估算半径与预设半径的误差
   - 调整 `PIXEL_TO_CM` 像素转换比例
   - 放宽动态容忍度系数

3. **密度过滤过严**
   - 提高密度阈值：`blob_density < 0.8` → `< 1.0`
   - 检查实际圆环的密度值

4. **检测性能问题**
   - 关闭调试输出（删除print语句）
   - 优化颜色阈值减少无关色块

## 硬件要求
- K210摄像头模块
- 串口通信支持
- LCD显示屏

## 注意事项
- 确保红色目标与背景有足够对比度
- 光线条件会影响检测精度
- 定期校准像素-厘米转换比例
- 增强标注显示详细的位置和半径信息，便于验证检测准确性

## 版本信息
- 版本：3.2
- 文件：vision_integrated.py (548行)
- 重大更新：添加霍夫圆检测和形态学处理，解决细圆环分割问题
- 主要改进：双重检测算法、形态学预处理、圆检测、智能选择机制
- 兼容性：完全兼容OpenMV/CanMV的MicroPython环境
